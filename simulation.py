"""
Simulation Modul für Battlesnake

Dieses Modul implementiert die Spielzustandssimulation für das Battlesnake-Spiel.
Es ist identisch mit tree.py und kann zukünftige Spielzustände basierend auf
gegebenen Bewegungen berechnen.

Hinweis: Diese Datei scheint ein Duplikat von tree.py zu sein.
"""

from copy import deepcopy
from utils import get_new_position


# =============================================================================================================================


def get_next_game_state(game_state, moves):
    """
    Simuliert den nächsten Spielzustand basierend auf gegebenen Bewegungen

    Diese Funktion ist das Herzstück der Spielsimulation. Sie nimmt den aktuellen
    Spielzustand und die Bewegungen aller Schlangen und berechnet den resultierenden
    Spielzustand nach einem Zug.

    Args:
        game_state (dict): Aktueller Spielzustand mit:
            - board: Spielfeldinformationen (width, height, snakes, food)
        moves (dict): Dictionary mit Bewegungen für jede Schlange:
                     {schlangen_id: bewegungsrichtung}

    Returns:
        dict: Neuer Spielzustand nach Ausführung der Bewegungen

    Simulationsschritte:
        1. Berechnung neuer Kopfpositionen für alle Schlangen
        2. Bewegung aller Schlangen (Kopf hinzufügen, Gesundheit reduzieren)
        3. Nahrungsaufnahme prüfen und verarbeiten
        4. Kopf-an-Kopf-Kollisionen auflösen
        5. Wand- und Körperkollisionen prüfen
        6. Tote Schlangen markieren

    Kollisionsregeln:
        - Kopf-an-Kopf: Längere Schlange überlebt, bei Gleichstand sterben beide
        - Körperkollision: Kollidierende Schlange stirbt
        - Wandkollision: Schlange stirbt
        - Verhungern: Schlange stirbt bei Gesundheit ≤ 0
    """
    new_state = deepcopy(game_state)  # call by value
    snakes = new_state['board']['snakes']
    food = new_state['board']['food']
    new_heads = {}

    # 1. Berechne neue Kopfpositionen
    for snake in snakes:
        if snake['health'] <= 0:
            continue
        move = moves[snake['id']]
        old_head = snake['body'][0]
        new_head = get_new_position(old_head, move)
        new_heads[snake['id']] = new_head

    # 2. Alle Schlangen bewegen (nur Kopf hinzufügen, Rest wird nachher angepasst)
    for snake in snakes:
        if snake['health'] <= 0:
            continue
        head = new_heads[snake['id']]
        snake['body'].insert(0, head)
        snake['health'] -= 1  # Gesundheitsverlust bei Bewegung

    # 3. Nahrung checken
    for snake in snakes:
        if snake['health'] <= 0:
            continue
        head = snake['body'][0]
        ate_food = False
        for f in food:
            if head['x'] == f['x'] and head['y'] == f['y']:
                ate_food = True
                snake['health'] = 100
                new_state['board']['food'].remove(f)
                break
        if not ate_food:
            snake['body'].pop()  # dann hat sich nämlich der Schwanz bewegt

    # 4. Kopf-an-Kopf Kollision
    positions = {}  # enthält die Positionen an der mindestens ein Schlangenkopf ist
    for snake in snakes:
        if snake['health'] <= 0:
            continue
        head = new_heads[snake['id']]
        positions.setdefault((head['x'], head['y']), []).append(
            snake)  # der Value des dictionarys ist also eine Liste von Schlangen, die diesen Ort als neuen Kopf haben

    for pos, colliders in positions.items():
        if len(colliders) > 1:
            max_length = max(len(s['body']) for s in colliders)
            # Prüfen ob es mehrere längste gibt
            longest_snakes = [s for s in colliders if len(s['body']) == max_length]
            if len(longest_snakes) > 1:
                # Alle Schlangen sterben
                for s in colliders:
                    s['health'] = 0
            else:
                # Nur die kürzeren sterben, längste bleiben am Leben
                for s in colliders:
                    if len(s['body']) < max_length:
                        s['health'] = 0

    # 5. Wände und Körperkollision

    body_positions = set()
    for snake in snakes:
        if snake['health'] > 0:
            # Ausschließlich echte Körperteile
            for segment in snake['body'][1:]:  # [1:] = (Köpfe wurden in 4. überrprüft)
                body_positions.add((segment['x'], segment['y']))

    for snake in snakes:
        if snake['health'] <= 0:
            continue
        head = new_heads[snake['id']]
        x, y = (head['x'], head['y'])

        # Kopf darf nicht außerhalb des Spielfedes sein
        board_height = game_state['board']['height']
        board_width = game_state['board']['width']

        if not (0 <= x < board_width and 0 <= y < board_height):
            snake['health'] = 0
            continue

        # mit Körperteilen kollidieren
        if (x, y) in body_positions:
            snake['health'] = 0

    return new_state
