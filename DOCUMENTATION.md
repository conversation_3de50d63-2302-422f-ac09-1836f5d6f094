# Battlesnake Bot "Würmchen V1" - Dokumentation

## Überblick

Dieses Projekt implementiert einen Battlesnake-Bot namens "Würmchen V1", der verschiedene Algorithmen zur Spielfeldanalyse und Zugauswahl verwendet.

## Architektur

### Hauptkomponenten

1. **main.py** - Battlesnake API-Endpunkte
   - `info()`: Schlangen-Konfiguration
   - `start()`: Spielbeginn-<PERSON>ler
   - `move()`: Hauptalgorithmus für Zugentscheidungen
   - `end()`: Spielende-Handler

2. **heuristik.py** - Spielfeldbewertung
   - `get_heuristik()`: Berechnet Heuristikwerte für jedes Spielfeld
   - `print_heuristik()`: Debug-Ausgabe der Heuristik-Matrix

3. **best_moves.py** - Zugauswahl-Algorithmen
   - `static_get_best_move()`: Statische Heuristik-basierte Z<PERSON>hl
   - `dynamic_get_best_move()`: Dynamische Simulation mit Gegnerbewegungen
   - `predict_opponent_moves()`: <PERSON><PERSON><PERSON><PERSON> von Gegnerzügen
   - `fallback_safe_direction()`: Fallback-Algorithmus

4. **tree.py / simulation.py** - Spielzustandssimulation
   - `get_next_game_state()`: Simuliert nächsten Spielzustand
   - **HINWEIS**: Tree-Funktionen fehlen (siehe unten)

5. **utils.py** - Hilfsfunktionen
   - `print_board()`: Farbige Spielfelddarstellung
   - `get_new_position()`: Positionsberechnung
   - `get_direction()`: Richtungsbestimmung
   - `position_in_board()`: Gültigkeitsprüfung
   - `get_blocked_positions()`: Blockierte Felder sammeln

6. **server.py** - Flask-Webserver
   - `run_server()`: Startet HTTP-Server für Battlesnake-API

## Algorithmus-Übersicht

### Heuristik-Berechnung

Die Heuristik bewertet jedes Spielfeld mit einem numerischen Wert:

- **Blockierte Felder**: -∞ (Schlangenkörper)
- **Spielfeldränder**: Leichte Bestrafung (-1)
- **Nahrung**: Bonus abhängig von Gesundheit (2 * (150-health))
- **Gegnernähe**: 
  - Orthogonal: -1100 (oder +500 wenn Gegner kleiner)
  - Diagonal: -44 (oder +200 wenn Gegner kleiner)
- **Dead-End-Erkennung**: Bestrafung für wenige freie Nachbarn
- **Bereichsgröße**: Bonus für größere zusammenhängende Bereiche
- **Gefahrenzonen**: -50 (Royale-Modus)

### Zugauswahl-Strategien

1. **Statischer Ansatz**: Verwendet vorberechnete Heuristik
2. **Dynamischer Ansatz**: Simuliert Gegnerbewegungen
3. **Fallback**: Wählt beste direkte Bewegung

## Fehlende Komponenten

**WICHTIGER HINWEIS**: Die folgenden Funktionen werden importiert, sind aber nicht implementiert:

### In tree.py fehlen:
- `build_static_tree()`: Baut statischen Suchbaum auf
- `build_dynamic_tree()`: Baut dynamischen Suchbaum mit Simulation auf
- `find_best_path()`: Findet besten Pfad durch Suchbaum
- `TreeNode` Klasse: Repräsentiert Knoten im Suchbaum

### Mögliche Implementierung der fehlenden Funktionen:

```python
class TreeNode:
    """Repräsentiert einen Knoten im Suchbaum"""
    def __init__(self, x, y, value, depth=0):
        self.x = x
        self.y = y
        self.value = value
        self.depth = depth
        self.children = []
        self.parent = None

def build_static_tree(heuristik, x, y, parent_dir, depth):
    """Baut statischen Suchbaum basierend auf Heuristik auf"""
    # Implementation fehlt
    pass

def build_dynamic_tree(you, game_state, depth, root_value):
    """Baut dynamischen Suchbaum mit Spielzustandssimulation auf"""
    # Implementation fehlt
    pass

def find_best_path(root, max_depth):
    """Findet besten Pfad durch den Suchbaum"""
    # Implementation fehlt
    pass
```

## Konfiguration

Die Heuristik-Parameter sind in `utils.py` definiert:

```python
heuristikValues = {
    "BORDER": -1,           # Spielfeldrand-Bestrafung
    "FOOD": 2,              # Nahrungsbonus-Multiplikator
    "OPPONENT_ORTHO": -1100, # Orthogonale Gegnernähe
    "OPPONENT_DIA": -44,    # Diagonale Gegnernähe
    "1_FREE": -94,          # 1 freier Nachbar
    "2_FREE": -83,          # 2 freie Nachbarn
    "3_FREE": -13,          # 3 freie Nachbarn
    "SIMULATION_DISTANCE": 3, # Suchtiefe
    "AREA": 5               # Bereichsgrößen-Bonus
}
```

## Verwendung

```bash
# Server starten
python main.py

# Der Server läuft auf http://0.0.0.0:8080
# Battlesnake-URL: http://your-server:8080
```

## Visualisierung

Das Spielfeld wird farbcodiert dargestellt:
- **Grün**: Gute Heuristikwerte
- **Gelb**: Neutrale Werte  
- **Rot**: Schlechte Heuristikwerte
- **Eigene Schlange**: HHH (Kopf), M (Körper)
- **Gegner**: HH? (Kopf), O (Körper)
- **Nahrung**: 🍎

## Nächste Schritte

1. **Implementierung der fehlenden Tree-Funktionen**
2. **Testing der Algorithmen**
3. **Parameter-Optimierung**
4. **Performance-Verbesserungen**
