"""
Server Modul für Battlesnake

Dieses Modul implementiert den Flask-Webserver, der die Battlesnake-API bereitstellt.
Der Server empfängt HTTP-Requests vom Battlesnake-Spiel-Server und leitet sie
an die entsprechenden Handler-Funktionen weiter.
"""

import logging
import os
import typing

from flask import Flask
from flask import request


def run_server(handlers: typing.Dict):
    """
    Startet den Flask-Webserver für die Battlesnake-API

    Erstellt eine Flask-Anwendung mit den erforderlichen API-Endpunkten
    und startet den Server auf dem konfigurierten Host und Port.

    Args:
        handlers (typing.Dict): Dictionary mit Handler-Funktionen:
            - "info": Funktion für GET / (Schlangen-Konfiguration)
            - "start": Funktion für POST /start (Spielbeginn)
            - "move": Funktion für POST /move (Zugentscheidung)
            - "end": Funktion für POST /end (Spielende)

    API-Endpunkte:
        - GET /: Gibt Schlangen-Konfiguration zurück (info)
        - POST /start: Wird bei Spielbeginn aufgerufen
        - POST /move: Wird für jeden Zug aufgerufen, erwartet Bewegung
        - POST /end: Wird bei Spielende aufgerufen

    Server-Konfiguration:
        - Host: 0.0.0.0 (alle Interfaces)
        - Port: Umgebungsvariable PORT oder 8080
        - Logging: Werkzeug-Logs auf ERROR-Level reduziert
    """
    app = Flask("Battlesnake")

    @app.get("/")
    def on_info():
        return handlers["info"]()

    @app.post("/start")
    def on_start():
        game_state = request.get_json()
        handlers["start"](game_state)
        return "ok"

    @app.post("/move")
    def on_move():
        game_state = request.get_json()
        return handlers["move"](game_state)

    @app.post("/end")
    def on_end():
        game_state = request.get_json()
        handlers["end"](game_state)
        return "ok"

    @app.after_request
    def identify_server(response):
        response.headers.set(
            "server", "battlesnake/github/starter-snake-python"
        )
        return response

    host = "0.0.0.0"
    port = int(os.environ.get("PORT", "8080"))

    logging.getLogger("werkzeug").setLevel(logging.ERROR)

    print(f"\nRunning Battlesnake at http://{host}:{port}")
    app.run(host=host, port=port)
