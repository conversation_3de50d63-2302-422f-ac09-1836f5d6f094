"""
Best Moves Modul für Battlesnake

Dieses Modul implementiert verschiedene Algorithmen zur Auswahl der besten Bewegung.
Es enthält sowohl statische als auch dynamische Ansätze zur Zugberechnung.
"""

from tree import build_static_tree, build_dynamic_tree, find_best_path
from heuristik import get_heuristik
from utils import get_new_position, get_direction, position_in_board

def fallback_safe_direction(heuristik, my_head):
    """
    Fallback-Algorithmus für sichere Bewegungsrichtung

    Wird verwendet, wenn die komplexeren Algorithmen keinen gültigen Pfad finden.
    Wählt die Bewegungsrichtung mit dem höchsten direkten Heuristikwert.

    Args:
        heuristik (list[list[float]]): 2D-A<PERSON>y mit Heuristikwerten
        my_head (dict): Position des Schlangenkopfes mit 'x' und 'y' Koordinaten

    Returns:
        str: Bewegungsrichtung ("up", "down", "left", "right")

    Algorithmus:
        - Bewertet alle vier möglichen Bewegungsrichtungen
        - Wählt die Richtung mit dem höchsten Heuristikwert
        - Berücksichtigt Spielfeldgrenzen (außerhalb = -∞)
    """
    directions = ["up", "down", "left", "right"]
    best_move = max(directions, key=lambda move: single_move_value(move, heuristik, my_head))
    return best_move

def single_move_value(move, heuristik, my_head):
    """
    Berechnet den Heuristikwert für eine einzelne Bewegung

    Hilfsfunktion zur Bewertung einer spezifischen Bewegungsrichtung
    basierend auf der aktuellen Heuristik-Matrix.

    Args:
        move (str): Bewegungsrichtung ("up", "down", "left", "right")
        heuristik (list[list[float]]): 2D-Array mit Heuristikwerten
        my_head (dict): Position des Schlangenkopfes mit 'x' und 'y' Koordinaten

    Returns:
        float: Heuristikwert der Zielposition, -∞ wenn außerhalb des Spielfelds

    Algorithmus:
        - Berechnet neue Position basierend auf Bewegungsrichtung
        - Prüft ob Position innerhalb des Spielfelds liegt
        - Gibt entsprechenden Heuristikwert zurück
    """
    new_position = get_new_position(my_head, move)
    x, y = new_position["x"], new_position["y"]
    return heuristik[y][x] if position_in_board(x, y, len(heuristik[0]), len(heuristik)) else -float('inf')

def static_get_best_move(simulation_distance, heuristik, you):
    """
    Statischer Algorithmus zur Bestimmung der besten Bewegung

    Verwendet eine vorberechnete Heuristik-Matrix und baut einen Suchbaum
    mit fester Tiefe auf, um die beste Bewegungssequenz zu finden.

    Args:
        simulation_distance (int): Suchtiefe für die Vorausschau (Anzahl Züge)
        heuristik (list[list[float]]): 2D-Array mit vorberechneten Heuristikwerten
        you (dict): Informationen über die eigene Schlange

    Returns:
        str: Beste Bewegungsrichtung ("up", "down", "left", "right")

    Algorithmus:
        1. Baut statischen Suchbaum mit gegebener Tiefe auf
        2. Findet besten Pfad durch den Baum
        3. Extrahiert erste Bewegung aus bestem Pfad
        4. Fallback auf sichere Richtung bei Problemen

    Hinweis:
        Dieser Algorithmus berücksichtigt keine Gegnerbewegungen oder
        Spielfeldveränderungen während der Simulation.
    """
    my_head = you['body'][0]
    x, y = my_head["x"], my_head["y"]

    root = build_static_tree(heuristik, x, y, parent_dir=None, depth=simulation_distance)
    best_path, best_weight = find_best_path(root, simulation_distance)

    if not best_path or len(best_path) < 2:
        best_move = fallback_safe_direction(heuristik, my_head)
        # print("Fallback auf sichere Richtung: ", best_move)
    else:
        best_move = get_direction(root, best_path[1])

    return best_move

def dynamic_get_best_move(simulation_distance, you, game_state):
    root = build_dynamic_tree(you, game_state, depth=simulation_distance, root_value=0)
    best_path, best_weight = find_best_path(root, simulation_distance)
    if not best_path or len(best_path) < 2:
        print("macht Unfug")
        best_move = "up"
    else:
        best_move = get_direction(root, best_path[1])
    return best_move

# ==============================================================================================

def predict_opponent_moves(opponents, game_state):
    opponent_moves = {}
    for opponent in opponents:
        heuristik = get_heuristik(opponent, game_state)
        move = static_get_best_move(2, heuristik, opponent)
        my_id = opponent['id']
        opponent_moves[opponent['id']] = move
    return opponent_moves

